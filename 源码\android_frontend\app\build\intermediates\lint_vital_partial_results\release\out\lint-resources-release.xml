http://schemas.android.com/apk/res-auto;;${\:app*release*sourceProvider*0*resDir*0}/values/colors.xml,${\:app*release*sourceProvider*0*resDir*0}/drawable/splash_background.xml,${\:app*release*sourceProvider*0*resDir*0}/drawable/ic_launcher_foreground.xml,${\:app*release*sourceProvider*0*resDir*0}/drawable/ic_launcher_background.xml,${\:app*release*sourceProvider*0*resDir*0}/drawable/ic_launcher.xml,${\:app*release*sourceProvider*0*resDir*0}/layout/activity_contract.xml,${\:app*release*sourceProvider*0*resDir*0}/layout/activity_main.xml,${\:app*release*sourceProvider*0*resDir*0}/layout/activity_splash.xml,${\:app*release*sourceProvider*0*resDir*0}/mipmap-anydpi-v26/ic_launcher_round.xml,${\:app*release*sourceProvider*0*resDir*0}/mipmap-anydpi-v26/ic_launcher.xml,${\:app*release*sourceProvider*0*resDir*0}/values/strings.xml,${\:app*release*sourceProvider*0*resDir*0}/values/styles.xml,${\:app*release*sourceProvider*0*resDir*0}/xml/network_security_config.xml,+color:colorPrimary,0,V"#FF6B35";colorPrimaryDark,0,V"#E55A2B";light_gray,0,V"#F5F5F5";black,0,V"#000000";splash_background_color,0,V"#FFFFFF";colorAccent,0,V"#FF6B35";gray,0,V"#808080";white,0,V"#FFFFFF";+drawable:splash_background,1,F;ic_launcher_foreground,2,F;ic_launcher_background,3,F;ic_launcher,4,F;+id:tv_title,5,F;btn_back,5,F;tv_error_message,5,F;btn_menu,5,F;bottom_actions,5,F;btn_save,5,F;btn_print,5,F;webView,6,F;webview_container,5,F;loading_layout,5,F;error_layout,5,F;btn_retry,5,F;+layout:activity_main,6,F;activity_contract,5,F;activity_splash,7,F;+mipmap:ic_launcher_round,8,F;ic_launcher,9,F;+string:loading_message,10,V"正在加载...";permission_granted,10,V"权限已授予";network_error,10,V"网络连接失败，请检查网络设置";permission_denied,10,V"权限被拒绝";contacts_permission_message,10,V"应用需要读取通讯录权限以提供更好的服务";contract_loading,10,V"正在加载合同...";contract_title,10,V"借款合同";contract_error,10,V"合同加载失败";contract_print,10,V"打印合同";app_name,10,V"优易花";contacts_permission_title,10,V"通讯录权限";contract_mobile_optimized,10,V"已为移动端优化";app_description,10,V"优易花小贷助手";retry,10,V"重试";contract_save,10,V"保存合同";+style:SplashTheme,11,VDTheme.AppCompat.Light.NoActionBar,android\:windowBackground:@drawable/splash_background,android\:windowNoTitle:true,android\:windowFullscreen:true,android\:windowActionBar:false,;AppTheme,11,VDTheme.AppCompat.Light.NoActionBar,colorPrimary:#FF6B35,colorPrimaryDark:#E55A2B,colorAccent:#FF6B35,android\:statusBarColor:#E55A2B,android\:navigationBarColor:#FF6B35,;+xml:network_security_config,12,F;