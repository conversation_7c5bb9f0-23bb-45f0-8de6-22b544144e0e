<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\仿随意花小贷源码完整版\源码\android_frontend\app\src\main\assets"><file name="contacts_bridge.js" path="E:\仿随意花小贷源码完整版\源码\android_frontend\app\src\main\assets\contacts_bridge.js"/><file name="contacts_test.html" path="E:\仿随意花小贷源码完整版\源码\android_frontend\app\src\main\assets\contacts_test.html"/><file name="CustomerData.php" path="E:\仿随意花小贷源码完整版\源码\android_frontend\app\src\main\assets\CustomerData.php"/><file name="customers.json" path="E:\仿随意花小贷源码完整版\源码\android_frontend\app\src\main\assets\customers.json"/><file name="customer_contract.php" path="E:\仿随意花小贷源码完整版\源码\android_frontend\app\src\main\assets\customer_contract.php"/><file name="customer_contract_mobile.php" path="E:\仿随意花小贷源码完整版\源码\android_frontend\app\src\main\assets\customer_contract_mobile.php"/><file name="customer_view.php" path="E:\仿随意花小贷源码完整版\源码\android_frontend\app\src\main\assets\customer_view.php"/><file name="debug_test.html" path="E:\仿随意花小贷源码完整版\源码\android_frontend\app\src\main\assets\debug_test.html"/><file name="final_test.html" path="E:\仿随意花小贷源码完整版\源码\android_frontend\app\src\main\assets\final_test.html"/><file name="test.html" path="E:\仿随意花小贷源码完整版\源码\android_frontend\app\src\main\assets\test.html"/><file name="权限调试测试页面.html" path="E:\仿随意花小贷源码完整版\源码\android_frontend\app\src\main\assets\权限调试测试页面.html"/></source><source path="E:\仿随意花小贷源码完整版\源码\android_frontend\app\build\intermediates\shader_assets\release\out"/></dataSet><dataSet config="release" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\仿随意花小贷源码完整版\源码\android_frontend\app\src\release\assets"/></dataSet></merger>