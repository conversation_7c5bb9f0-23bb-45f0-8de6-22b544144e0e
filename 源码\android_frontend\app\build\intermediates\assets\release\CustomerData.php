<?php
/**
 * 客户数据管理类 - 使用JSON文件模拟数据库
 */

class CustomerData {
    private $dataFile = 'customers.json';
    
    public function __construct() {
        // 如果数据文件不存在，创建初始数据
        if (!file_exists($this->dataFile)) {
            $this->initializeData();
        }
    }
    
    // 初始化数据
    private function initializeData() {
        $initialData = array(
            1 => array('id' => 1, 'customer_name' => '张三', 'phone' => '***********', 'id_card' => '110101199001011234', 'bank_card' => '6222021234567890123', 'bank_name' => '中国工商银行', 'loan_amount' => 5000, 'loan_periods' => 12, 'loan_status' => '已放款', 'created_time' => '2024-01-15 10:30:00'),
            2 => array('id' => 2, 'customer_name' => '李四', 'phone' => '***********', 'id_card' => '110101199002021234', 'bank_card' => '6222021234567890124', 'bank_name' => '中国建设银行', 'loan_amount' => 8000, 'loan_periods' => 24, 'loan_status' => '审核中', 'created_time' => '2024-01-16 14:20:00'),
            3 => array('id' => 3, 'customer_name' => '王五', 'phone' => '***********', 'id_card' => '110101199003031234', 'bank_card' => '6222021234567890125', 'bank_name' => '中国农业银行', 'loan_amount' => 3000, 'loan_periods' => 6, 'loan_status' => '已还款', 'created_time' => '2024-01-17 09:15:00'),
            4 => array('id' => 4, 'customer_name' => '赵六', 'phone' => '***********', 'id_card' => '110101199004041234', 'bank_card' => '6222021234567890126', 'bank_name' => '招商银行', 'loan_amount' => 10000, 'loan_periods' => 36, 'loan_status' => '逾期', 'created_time' => '2024-01-18 16:45:00'),
            5 => array('id' => 5, 'customer_name' => '钱七', 'phone' => '***********', 'id_card' => '110101199005051234', 'bank_card' => '6222021234567890127', 'bank_name' => '交通银行', 'loan_amount' => 6000, 'loan_periods' => 18, 'loan_status' => '已放款', 'created_time' => '2024-01-19 11:30:00'),
        );
        $this->saveData($initialData);
    }
    
    // 读取所有数据
    public function getAllCustomers() {
        if (!file_exists($this->dataFile)) {
            return array();
        }
        $data = file_get_contents($this->dataFile);
        return json_decode($data, true) ?: array();
    }
    
    // 根据ID获取客户
    public function getCustomerById($id) {
        $customers = $this->getAllCustomers();
        return isset($customers[$id]) ? $customers[$id] : null;
    }
    
    // 添加客户
    public function addCustomer($customerData) {
        $customers = $this->getAllCustomers();
        
        // 生成新ID
        $newId = empty($customers) ? 1 : max(array_keys($customers)) + 1;
        
        $customerData['id'] = $newId;
        $customerData['created_time'] = date('Y-m-d H:i:s');
        
        $customers[$newId] = $customerData;
        
        return $this->saveData($customers) ? $newId : false;
    }
    
    // 更新客户
    public function updateCustomer($id, $customerData) {
        $customers = $this->getAllCustomers();
        
        if (!isset($customers[$id])) {
            return false;
        }
        
        // 保留原有的ID和创建时间
        $customerData['id'] = $id;
        $customerData['created_time'] = $customers[$id]['created_time'];
        $customerData['updated_time'] = date('Y-m-d H:i:s');
        
        $customers[$id] = $customerData;
        
        return $this->saveData($customers);
    }
    
    // 删除客户
    public function deleteCustomer($id) {
        $customers = $this->getAllCustomers();
        
        if (!isset($customers[$id])) {
            return false;
        }
        
        unset($customers[$id]);
        
        return $this->saveData($customers);
    }
    
    // 搜索客户
    public function searchCustomers($keyword) {
        $customers = $this->getAllCustomers();
        $results = array();
        
        foreach ($customers as $customer) {
            if (stripos($customer['customer_name'], $keyword) !== false ||
                stripos($customer['phone'], $keyword) !== false ||
                stripos($customer['id_card'], $keyword) !== false) {
                $results[] = $customer;
            }
        }
        
        return $results;
    }
    
    // 保存数据到文件
    private function saveData($data) {
        $json = json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        return file_put_contents($this->dataFile, $json) !== false;
    }
    
    // 获取统计信息
    public function getStatistics() {
        $customers = $this->getAllCustomers();
        $stats = array(
            'total' => count($customers),
            'approved' => 0,
            'pending' => 0,
            'completed' => 0,
            'overdue' => 0
        );
        
        foreach ($customers as $customer) {
            switch ($customer['loan_status']) {
                case '已放款':
                    $stats['approved']++;
                    break;
                case '审核中':
                    $stats['pending']++;
                    break;
                case '已还款':
                    $stats['completed']++;
                    break;
                case '逾期':
                    $stats['overdue']++;
                    break;
            }
        }
        
        return $stats;
    }
}
?>
