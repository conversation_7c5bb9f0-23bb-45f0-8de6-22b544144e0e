<?php
/**
 * 客户借款合同页面 - 移动端H5优化版
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();
require_once 'CustomerData.php';

// 获取参数
$customer_id = isset($_GET['id']) ? intval($_GET['id']) : 1;
$is_user_mode = isset($_GET['user']) && $_GET['user'] == '1';

// 权限检查
if (!$is_user_mode) {
    if (!isset($_SESSION['admin_logged_in'])) {
        $_SESSION['admin_logged_in'] = true; // 测试模式
    }
}

// 获取客户数据
$customerDB = new CustomerData();
$customer = $customerDB->getCustomerById($customer_id);

if (!$customer) {
    $customer = array(
        'id' => $customer_id,
        'customer_name' => '客户' . $customer_id,
        'id_card' => '110101199001011234',
        'phone' => '***********',
        'address' => '北京市朝阳区测试地址',
        'loan_amount' => 50000.00,
        'loan_periods' => 12,
        'bank_card' => '6222021234567890123',
        'bank_name' => '中国工商银行',
        'loan_status' => '已放款',
        'contract_no' => 'XD' . date('Ymd') . sprintf('%04d', $customer_id),
        'party_a_name' => '甲方（借款人）',
        'party_b_name' => '乙方（贷款人）',
        'company_name' => '随意花金融有限公司',
        'company_phone' => '************',
        'company_address' => '北京市朝阳区金融街123号',
        'company_manager' => '张经理',
        'sign_date' => date('Y年m月d日'),
        'monthly_rate' => 2.0,
        'overdue_mark' => 0
    );
}

// 统一字段名
$customer['name'] = $customer['customer_name'];
$customer['id_number'] = $customer['id_card'];

// 生成合同编号
$contract_no = isset($customer['contract_no']) ? $customer['contract_no'] : 'XD' . date('Ymd') . sprintf('%04d', $customer['id']);
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="format-detection" content="telephone=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <title>借款合同 - 移动端</title>
    <style>
        /* 移动端重置样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            -webkit-tap-highlight-color: transparent;
        }
        
        /* 全局强制横向显示 - 更简单直接的方法 */
        body, div, p, h1, h2, h3, h4, h5, h6, span, a, button, input, select, textarea {
            writing-mode: horizontal-tb !important;
            text-orientation: mixed !important;
            direction: ltr !important;
            text-align: left !important;
            unicode-bidi: isolate !important;
        }
        .contract-clause, .signature-area, .signature-line,
        button, a, strong, em {
            writing-mode: horizontal-tb !important;
            text-orientation: mixed !important;
            direction: ltr !important;
            text-combine-upright: none !important;
            text-orientation: mixed !important;
            font-feature-settings: normal !important;
        }

        /* 特别针对包含emoji的元素 */
        h1, h3, h4, button {
            display: block !important;
            white-space: nowrap !important;
            writing-mode: horizontal-tb !important;
            text-orientation: mixed !important;
            direction: ltr !important;
        }

        /* 强制所有文字内容横排 */
        .mobile-header h1,
        .info-card h3,
        .contract-clause h4,
        .mobile-btn {
            writing-mode: horizontal-tb !important;
            text-orientation: mixed !important;
            direction: ltr !important;
            display: inline-block !important;
            vertical-align: top !important;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background: white;
            color: #333;
            font-size: 12px; /* 再次调小基础字体 */
            line-height: 1.4; /* 减小行高 */
            -webkit-font-smoothing: antialiased;
            -webkit-text-size-adjust: 100%; /* 禁止字体大小自动调整 */
            padding-bottom: 50px; /* 减小底部空间 */
            /* 强制横排文字 */
            writing-mode: horizontal-tb !important;
            text-orientation: mixed !important;
            direction: ltr !important;
        }

        /* 移动端头部 - 更紧凑 */
        .mobile-header {
            background: white;
            padding: 8px 10px; /* 减小内边距 */
            text-align: center;
            /* 去掉下边框 */
            border-bottom: none;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .mobile-header h1 {
            font-size: 16px; /* 再次调小标题字体 */
            font-weight: bold;
            color: #333;
            margin-bottom: 2px;
        }

        .mobile-header .subtitle {
            font-size: 10px; /* 再次调小副标题字体 */
            color: #666;
        }

        /* 移动端容器 - 更紧凑 */
        .mobile-container {
            background: white;
            margin: 5px; /* 减小外边距 */
            /* 去掉圆角和边框 */
            border-radius: 0;
            overflow: hidden;
            border: none;
        }
        
        /* 移动端信息卡片 - 超紧凑 */
        .info-card {
            padding: 8px 10px; /* 减小内边距 */
            /* 去掉下边框 */
            border-bottom: none;
        }

        .info-card:last-child {
            border-bottom: none;
        }

        .info-card h3 {
            font-size: 14px; /* 再次调小卡片标题字体 */
            color: #333;
            margin-bottom: 6px;
            padding-bottom: 4px;
            /* 去掉下边框 */
            border-bottom: none;
            font-weight: bold;
        }

        .info-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 6px 0; /* 减小内边距 */
            /* 去掉下边框 */
            border-bottom: none;
            width: 100%;
        }

        .info-row:last-child {
            border-bottom: none;
        }

        .info-label {
            font-weight: normal;
            color: #666;
            font-size: 12px; /* 再次调小标签字体 */
            min-width: 70px; /* 减小最小宽度 */
            display: block;
            float: left;
            text-align: left;
        }

        .info-value {
            color: #333;
            font-size: 12px; /* 再次调小内容字体 */
            text-align: right;
            flex: 1;
            word-break: break-all;
            float: right;
        }
        
        /* 合同条款 - 紧凑版 */
        .contract-clause {
            padding: 10px 12px;
            /* 去掉背景色和圆角 */
            background: none;
            margin: 5px;
            border-radius: 0;
            font-size: 11px; /* 再次调小条款字体 */
            line-height: 1.5;
            text-align: left;
            width: 100%;
        }

        .contract-clause h4 {
            font-size: 13px; /* 再次调小条款标题字体 */
            margin-bottom: 5px;
            color: #333;
            display: block;
            text-align: left;
        }

        .contract-clause p {
            margin-bottom: 5px;
            text-align: left;
            word-break: break-word;
            display: block;
            width: 100%;
        }

        .contract-clause strong {
            display: inline;
            font-weight: bold;
        }

        /* 签名区域 - 紧凑版 */
        .signature-area {
            padding: 12px;
            background: white;
            margin: 5px;
            /* 去掉圆角和边框 */
            border-radius: 0;
            display: flex;
            justify-content: space-between;
            border: none;
            width: 100%;
            text-align: center;
        }

        .signature-box {
            text-align: center;
            flex: 1;
            display: inline-block;
            width: 48%;
        }

        .signature-line {
            border-bottom: 1px solid #333;
            height: 25px;
            margin-bottom: 5px;
            line-height: 25px;
            font-weight: bold;
            font-size: 12px; /* 再次调小签名区域字体 */
            display: block;
            text-align: center;
        }
        
        /* 底部操作栏 - 紧凑版 */
        .mobile-actions {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            padding: 8px 10px;
            /* 去掉上边框 */
            border-top: none;
            display: flex;
            gap: 8px;
            z-index: 100;
        }

        .mobile-btn {
            flex: 1;
            padding: 10px; /* 减小按钮内边距 */
            border: none;
            border-radius: 4px;
            font-size: 14px; /* 再次调小按钮字体 */
            font-weight: bold;
            cursor: pointer;
            text-decoration: none;
            text-align: center;
            display: block;
        }

        .mobile-btn.primary {
            background: #007aff;
            color: white;
        }

        .mobile-btn.secondary {
            background: #f0f0f0;
            color: #333;
        }
        
        /* 逾期标记 */
        .overdue-mark {
            color: #ff3b30;
            font-weight: bold;
            font-size: 16px; /* 再次调小逾期标记字体 */
        }
        
        /* 强制所有元素使用横向布局 */
        table {
            table-layout: fixed;
            width: 100%;
            border-collapse: collapse;
        }
        
        table td {
            word-break: break-all;
        }
        
        /* 添加针对Android WebView的特殊优化 */
        @supports (-webkit-appearance:none) {
            body {
                transform: rotate(0deg);
                transform-origin: top left;
            }
            
            h1, h2, h3, h4, h5, h6, p, div, span {
                transform: rotate(0deg);
            }
        }
        
        /* 响应式调整 - 超紧凑 */
        @media (max-width: 375px) {
            body {
                font-size: 11px;
                padding-bottom: 45px;
            }

            .mobile-container {
                margin: 3px;
            }

            .info-card {
                padding: 8px 10px;
            }

            .mobile-header {
                padding: 8px 10px;
            }

            .mobile-header h1 {
                font-size: 14px;
            }

            .mobile-header .subtitle {
                font-size: 9px;
            }

            .info-label, .info-value {
                font-size: 11px;
            }

            .contract-clause {
                font-size: 10px;
                padding: 8px 10px;
            }

            .signature-line {
                height: 22px;
                line-height: 22px;
                font-size: 11px;
            }

            .mobile-btn {
                padding: 8px;
                font-size: 13px;
            }
        }
        
        /* 打印样式 */
        @media print {
            .mobile-header, .mobile-actions {
                display: none !important;
            }
            
            body {
                background: white;
                padding-bottom: 0;
            }
            
            .mobile-container {
                margin: 0;
                box-shadow: none;
                border-radius: 0;
            }
        }
    </style>
</head>
<body>
    <!-- 移动端头部 -->
    <div class="mobile-header">
        <h1 style="text-align:center; display:block;">
            借款合同
            <?php if (isset($customer['overdue_mark']) && $customer['overdue_mark'] == 1): ?>
                <span class="overdue-mark">逾期</span>
            <?php endif; ?>
        </h1>
        <div class="subtitle" style="text-align:center;">合同编号：<?php echo htmlspecialchars($contract_no); ?></div>
    </div>

    <!-- 借款人信息 -->
    <div class="mobile-container">
        <div class="info-card">
            <h3>👤 借款人信息</h3>
            <div class="info-row">
                <span class="info-label">姓名</span>
                <span class="info-value"><?php echo htmlspecialchars($customer['name']); ?></span>
            </div>
            <div class="info-row">
                <span class="info-label">身份证</span>
                <span class="info-value"><?php echo htmlspecialchars($customer['id_number']); ?></span>
            </div>
            <div class="info-row">
                <span class="info-label">电话</span>
                <span class="info-value"><?php echo htmlspecialchars($customer['phone']); ?></span>
            </div>
            <div class="info-row">
                <span class="info-label">地址</span>
                <span class="info-value"><?php echo htmlspecialchars($customer['address']); ?></span>
            </div>
        </div>
    </div>

    <!-- 借款信息 -->
    <div class="mobile-container">
        <div class="info-card">
            <h3>💰 借款信息</h3>
            <div class="info-row">
                <span class="info-label">借款金额</span>
                <span class="info-value">¥<?php echo number_format($customer['loan_amount'], 2); ?></span>
            </div>
            <div class="info-row">
                <span class="info-label">借款期限</span>
                <span class="info-value"><?php echo $customer['loan_periods']; ?>个月</span>
            </div>
            <div class="info-row">
                <span class="info-label">月利率</span>
                <span class="info-value"><?php echo isset($customer['monthly_rate']) ? $customer['monthly_rate'] : 2.0; ?>%</span>
            </div>
            <div class="info-row">
                <span class="info-label">放款状态</span>
                <span class="info-value" style="<?php echo (isset($customer['loan_status']) && $customer['loan_status'] == '逾期') ? 'color: #ff3b30; font-weight: bold;' : ''; ?>">
                    <?php echo htmlspecialchars(isset($customer['loan_status']) ? $customer['loan_status'] : '已放款'); ?>
                </span>
            </div>
        </div>
    </div>

    <!-- 银行信息 -->
    <div class="mobile-container">
        <div class="info-card">
            <h3>🏦 银行信息</h3>
            <div class="info-row">
                <span class="info-label">银行卡号</span>
                <span class="info-value"><?php echo htmlspecialchars(isset($customer['bank_card']) ? $customer['bank_card'] : ''); ?></span>
            </div>
            <div class="info-row">
                <span class="info-label">开户银行</span>
                <span class="info-value"><?php echo htmlspecialchars(isset($customer['bank_name']) ? $customer['bank_name'] : ''); ?></span>
            </div>
        </div>
    </div>

    <!-- 合同条款 -->
    <div class="contract-clause">
        <h4 style="text-align:left;">合同条款</h4>
        <table style="width:100%; table-layout:fixed;">
            <tr>
                <td style="text-align:left; padding:3px 0;">
                    <div><span style="font-weight:bold;">借款用途：</span><?php echo htmlspecialchars(isset($customer['loan_purpose']) ? $customer['loan_purpose'] : '甲方向乙方借款用于个人消费，包括但不限于购物、装修、旅游、教育等。'); ?></div>
                </td>
            </tr>
            <tr>
                <td style="text-align:left; padding:3px 0;">
                    <div><span style="font-weight:bold;">还款方式：</span><?php echo htmlspecialchars(isset($customer['repayment_method']) ? $customer['repayment_method'] : '甲方按月等额本息还款，每月还款金额根据借款金额和期限计算。'); ?></div>
                </td>
            </tr>
            <tr>
                <td style="text-align:left; padding:3px 0;">
                    <div><span style="font-weight:bold;">违约责任：</span><?php echo htmlspecialchars(isset($customer['penalty_clause']) ? $customer['penalty_clause'] : '甲方未按时足额还款，每逾期一日，按照应还款金额的0.05%支付违约金。'); ?></div>
                </td>
            </tr>
            <tr>
                <td style="text-align:left; padding:3px 0;">
                    <div><span style="font-weight:bold;">其他条款：</span><?php echo htmlspecialchars(isset($customer['other_terms']) ? $customer['other_terms'] : '本合同自双方签字（或盖章）之日起生效，具有法律效力。'); ?></div>
                </td>
            </tr>
        </table>
    </div>

    <!-- 签名区域 -->
    <div class="signature-area">
        <div class="signature-box">
            <div class="signature-line">甲方签字</div>
            <div><?php echo htmlspecialchars($customer['name']); ?></div>
        </div>
        <div class="signature-box">
            <div class="signature-line">乙方签字</div>
            <div><?php echo htmlspecialchars(isset($customer['company_manager']) ? $customer['company_manager'] : '张经理'); ?></div>
        </div>
    </div>

    <!-- 底部操作栏 -->
    <div class="mobile-actions">
        <button onclick="window.print()" class="mobile-btn secondary">🖨️ 打印</button>
        <?php if (!$is_user_mode): ?>
            <a href="customer_edit.php?id=<?php echo $customer_id; ?>" class="mobile-btn primary">✏️ 编辑</a>
        <?php endif; ?>
    </div>

    <script>
        // 移动端优化
        document.addEventListener('DOMContentLoaded', function() {
            // 禁用双击缩放
            let lastTouchEnd = 0;
            document.addEventListener('touchend', function(event) {
                const now = (new Date()).getTime();
                if (now - lastTouchEnd <= 300) {
                    event.preventDefault();
                }
                lastTouchEnd = now;
            }, false);
            
            // 防止页面滚动时的橡皮筋效果
            document.body.addEventListener('touchmove', function(e) {
                if (e.target === document.body) {
                    e.preventDefault();
                }
            }, { passive: false });
            
            // 强制所有元素横向显示
            const allElements = document.querySelectorAll('*');
            allElements.forEach(function(el) {
                el.style.writingMode = 'horizontal-tb';
                el.style.textOrientation = 'mixed';
                el.style.direction = 'ltr';
            });
            
            console.log('移动端合同页面已加载');
        });
    </script>
</body>
</html>
