<?php
/**
 * 查看客户详情页面
 */

session_start();

// 引入客户数据管理类（与其他页面保持一致）
require_once 'CustomerData.php';

// 简单的登录检查（测试模式跳过）
if (!isset($_GET['test']) && (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true)) {
    header('Location: admin.php');
    exit;
}

// 获取客户ID
$customer_id = isset($_GET['id']) ? intval($_GET['id']) : 0;

if (!$customer_id) {
    header('Location: customer_management.php');
    exit;
}

// 使用CustomerData类读取真实数据（与customer_edit.php和customer_contract.php保持一致）
$customerDB = new CustomerData();
$customer = $customerDB->getCustomer($customer_id);

if (!$customer) {
    // 如果客户不存在，重定向到客户管理页面
    header('Location: customer_management.php');
    exit;
}

// 确保必要字段存在，设置默认值
$customer = array_merge(array(
    'id' => $customer_id,
    'customer_name' => '未知客户',
    'phone' => '',
    'id_card' => '',
    'address' => '',
    'loan_amount' => 0,
    'loan_periods' => 12,
    'loan_status' => '未知',
    'bank_card' => '',
    'bank_name' => '',
    'created_time' => date('Y-m-d H:i:s')
), $customer);

// 基于真实客户数据生成借款记录
$loan_records = array();
if ($customer['loan_amount'] > 0) {
    $loan_records[] = array(
        'id' => $customer['id'],
        'amount' => $customer['loan_amount'],
        'status' => isset($customer['loan_status']) ? $customer['loan_status'] : '已放款',
        'apply_time' => isset($customer['created_time']) ? $customer['created_time'] : date('Y-m-d H:i:s'),
        'approve_time' => isset($customer['created_time']) ? date('Y-m-d H:i:s', strtotime($customer['created_time']) + 3600) : date('Y-m-d H:i:s'),
        'loan_time' => isset($customer['created_time']) ? date('Y-m-d H:i:s', strtotime($customer['created_time']) + 7200) : date('Y-m-d H:i:s'),
        'periods' => isset($customer['loan_periods']) ? $customer['loan_periods'] : 12
    );
}

// 基于借款状态生成还款记录
$repayment_records = array();
if (isset($customer['loan_status']) && in_array($customer['loan_status'], ['已还款', '部分还款'])) {
    if ($customer['loan_status'] == '已还款') {
        // 计算总还款金额（含利息）
        $periods = isset($customer['loan_periods']) ? $customer['loan_periods'] : 12;
        $monthly_rate = 0.02; // 2%月利率
        $total_amount = $customer['loan_amount'] * (1 + $monthly_rate * $periods);

        $repayment_records[] = array(
            'id' => 1,
            'amount' => $total_amount,
            'type' => '正常还款',
            'time' => date('Y-m-d H:i:s', strtotime($customer['created_time']) + ($periods * 30 * 24 * 3600)),
            'method' => '银行转账'
        );
    } else {
        // 部分还款
        $partial_amount = $customer['loan_amount'] * 0.3; // 30%部分还款
        $repayment_records[] = array(
            'id' => 1,
            'amount' => $partial_amount,
            'type' => '部分还款',
            'time' => date('Y-m-d H:i:s', strtotime($customer['created_time']) + (15 * 24 * 3600)),
            'method' => '支付宝'
        );
    }
}

?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>客户详情 - 小贷系统</title>
    <style>
        body { margin: 0; font-family: Arial, sans-serif; background: #f5f5f5; }
        .admin-header { background: #393D49; color: white; height: 60px; line-height: 60px; padding: 0 20px; }
        .admin-main { padding: 20px; }
        .breadcrumb { background: white; padding: 15px; margin-bottom: 20px; border-radius: 5px; }
        .content-box { background: white; padding: 30px; border-radius: 5px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin-bottom: 20px; }
        .info-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .info-item { padding: 15px; background: #f8f9fa; border-radius: 5px; border-left: 4px solid #007bff; }
        .info-label { font-weight: bold; color: #333; margin-bottom: 5px; }
        .info-value { color: #666; font-size: 16px; }
        .btn { padding: 12px 24px; border: none; border-radius: 5px; cursor: pointer; text-decoration: none; display: inline-block; font-size: 14px; margin: 5px; }
        .btn-primary { background: #009688; color: white; }
        .btn-secondary { background: #6c757d; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn:hover { opacity: 0.9; }
        table { width: 100%; border-collapse: collapse; margin-top: 15px; }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid #e6e6e6; }
        th { background: #f2f2f2; font-weight: bold; }
        tr:hover { background: #f8f9fa; }
        .status-approved { color: #28a745; }
        .status-pending { color: #ffc107; }
        .status-completed { color: #17a2b8; }
        .status-overdue { color: #dc3545; }
        .section-title { display: flex; align-items: center; margin-bottom: 15px; font-size: 18px; color: #333; }
        .section-title span { margin-right: 8px; }
    </style>
</head>
<body>
    <!-- 头部导航 -->
    <div class="admin-header">
        <span>🏠 小贷系统 - 后台管理</span>
        <span style="float: right;">
            <a href="admin.php" style="color: #bdc3c7; margin-left: 20px;">🔙 返回首页</a>
            <a href="admin.php?logout=1" style="color: #bdc3c7; margin-left: 20px;">🚪 退出</a>
        </span>
    </div>

    <!-- 主要内容区域 -->
    <div class="admin-main">
        <!-- 面包屑导航 -->
        <div class="breadcrumb">
            <span>当前位置：</span>
            <a href="admin.php">管理首页</a> > 
            <a href="customer_management.php">客户管理</a> > 
            <span>客户详情</span>
        </div>

        <!-- 客户基本信息 -->
        <div class="content-box">
            <h2>👤 客户详情 - <?php echo htmlspecialchars($customer['customer_name']); ?></h2>
            
            <div class="info-grid">
                <div class="info-item">
                    <div class="info-label">客户ID</div>
                    <div class="info-value"><?php echo $customer['id']; ?></div>
                </div>
                <div class="info-item">
                    <div class="info-label">客户姓名</div>
                    <div class="info-value"><?php echo htmlspecialchars($customer['customer_name']); ?></div>
                </div>
                <div class="info-item">
                    <div class="info-label">手机号码</div>
                    <div class="info-value"><?php echo htmlspecialchars($customer['phone']); ?></div>
                </div>
                <div class="info-item">
                    <div class="info-label">身份证号</div>
                    <div class="info-value"><?php echo $customer['id_card'] ? substr($customer['id_card'], 0, 6) . '****' . substr($customer['id_card'], -4) : '未填写'; ?></div>
                </div>
                <div class="info-item">
                    <div class="info-label">联系地址</div>
                    <div class="info-value"><?php echo htmlspecialchars($customer['address'] ?: '未填写'); ?></div>
                </div>
                <div class="info-item">
                    <div class="info-label">银行卡号</div>
                    <div class="info-value"><?php echo $customer['bank_card'] ? substr($customer['bank_card'], 0, 4) . ' **** **** ' . substr($customer['bank_card'], -4) : '未填写'; ?></div>
                </div>
                <div class="info-item">
                    <div class="info-label">开户银行</div>
                    <div class="info-value"><?php echo htmlspecialchars($customer['bank_name'] ?: '未填写'); ?></div>
                </div>
                <div class="info-item">
                    <div class="info-label">当前借款金额</div>
                    <div class="info-value">¥<?php echo number_format($customer['loan_amount']); ?></div>
                </div>
                <div class="info-item">
                    <div class="info-label">借款期限</div>
                    <div class="info-value"><?php echo $customer['loan_periods']; ?> 个月</div>
                </div>
                <div class="info-item">
                    <div class="info-label">借款状态</div>
                    <div class="info-value">
                        <?php
                        $status_class = '';
                        switch($customer['loan_status']) {
                            case '已放款': $status_class = 'status-approved'; break;
                            case '审核中': $status_class = 'status-pending'; break;
                            case '已还款': $status_class = 'status-completed'; break;
                            case '逾期': $status_class = 'status-overdue'; break;
                        }
                        ?>
                        <span class="<?php echo $status_class; ?>">● <?php echo $customer['loan_status']; ?></span>
                    </div>
                </div>
                <div class="info-item">
                    <div class="info-label">创建时间</div>
                    <div class="info-value"><?php echo $customer['created_time']; ?></div>
                </div>
            </div>
            
            <div style="text-align: center; margin-top: 20px;">
                <a href="customer_edit.php?id=<?php echo $customer['id']; ?>" class="btn btn-primary">✏️ 编辑客户</a>
                <a href="customer_contract.php?id=<?php echo $customer['id']; ?>" class="btn btn-warning">📄 查看合同</a>
                <a href="#" class="btn btn-secondary" onclick="return confirm('确定要删除这个客户吗？')">🗑️ 删除客户</a>
                <a href="customer_management.php" class="btn btn-secondary">🔙 返回列表</a>
            </div>
        </div>

        <!-- 借款记录 -->
        <div class="content-box">
            <h2 class="section-title">
                <span>💰</span>
                借款记录
            </h2>
            
            <table>
                <thead>
                    <tr>
                        <th>借款ID</th>
                        <th>借款金额</th>
                        <th>借款期限</th>
                        <th>申请时间</th>
                        <th>审核时间</th>
                        <th>放款时间</th>
                        <th>状态</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($loan_records)): ?>
                    <tr>
                        <td colspan="7" style="text-align: center; color: #999; padding: 30px;">暂无借款记录</td>
                    </tr>
                    <?php else: ?>
                    <?php foreach ($loan_records as $record): ?>
                    <tr>
                        <td><?php echo $record['id']; ?></td>
                        <td>¥<?php echo number_format($record['amount']); ?></td>
                        <td><?php echo isset($record['periods']) ? $record['periods'] . ' 个月' : '-'; ?></td>
                        <td><?php echo $record['apply_time']; ?></td>
                        <td><?php echo $record['approve_time']; ?></td>
                        <td><?php echo $record['loan_time']; ?></td>
                        <td>
                            <?php
                            $status_class = '';
                            switch($record['status']) {
                                case '已放款': $status_class = 'status-approved'; break;
                                case '审核中': $status_class = 'status-pending'; break;
                                case '已还款': $status_class = 'status-completed'; break;
                                case '逾期': $status_class = 'status-overdue'; break;
                            }
                            ?>
                            <span class="<?php echo $status_class; ?>">● <?php echo $record['status']; ?></span>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

        <!-- 还款记录 -->
        <div class="content-box">
            <h2 class="section-title">
                <span>💳</span>
                还款记录
            </h2>
            
            <table>
                <thead>
                    <tr>
                        <th>还款ID</th>
                        <th>还款金额</th>
                        <th>还款类型</th>
                        <th>还款时间</th>
                        <th>还款方式</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($repayment_records)): ?>
                    <tr>
                        <td colspan="5" style="text-align: center; color: #999; padding: 30px;">暂无还款记录</td>
                    </tr>
                    <?php else: ?>
                    <?php foreach ($repayment_records as $record): ?>
                    <tr>
                        <td><?php echo $record['id']; ?></td>
                        <td>¥<?php echo number_format($record['amount']); ?></td>
                        <td><?php echo $record['type']; ?></td>
                        <td><?php echo $record['time']; ?></td>
                        <td><?php echo $record['method']; ?></td>
                    </tr>
                    <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</body>
</html>
