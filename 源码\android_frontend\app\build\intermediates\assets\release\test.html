<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>权限测试工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 15px;
            line-height: 1.5;
            background: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .btn {
            display: block;
            width: 100%;
            padding: 12px;
            margin: 8px 0;
            font-size: 14px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            background: #007cba;
            color: white;
            text-align: center;
        }
        .btn:active {
            background: #005a8b;
        }
        .debug-btn {
            background: #28a745;
        }
        .debug-btn:active {
            background: #218838;
        }
        .status {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 12px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
        .log {
            background: #343a40;
            color: #fff;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            max-height: 250px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 11px;
        }
        .success {
            color: #28a745;
        }
        .error {
            color: #dc3545;
        }
        .warning {
            color: #ffc107;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 权限调试工具</h1>
        
        <button class="btn debug-btn" onclick="checkStatus()">1. 检查当前状态</button>
        <div id="status" class="status">等待检查状态...</div>
        
        <button class="btn debug-btn" onclick="testDirectRequest()">2. 直接权限申请 ⭐</button>
        
        <button class="btn" onclick="testForceRequest()">3. 强制权限测试</button>
        
        <button class="btn" onclick="testInterface()">4. 测试JS接口</button>
        
        <button class="btn" onclick="readContacts()">5. 读取通讯录</button>
        
        <h3>📋 实时日志</h3>
        <div id="log" class="log">等待操作...</div>
        
        <button class="btn debug-btn" onclick="clearLog()">清空日志</button>
        
        <div style="margin-top: 20px; padding: 10px; background: #e9ecef; border-radius: 5px; font-size: 12px;">
            <strong>使用说明：</strong><br>
            1. 先点击"检查当前状态"查看基础信息<br>
            2. 重点测试"直接权限申请"（推荐）<br>
            3. 观察日志输出和权限弹窗<br>
            4. 如果权限弹窗不出现或无响应，截图反馈
        </div>
    </div>

    <script>
        let logContainer = document.getElementById('log');
        let statusContainer = document.getElementById('status');
        
        function log(message, type = 'normal') {
            let time = new Date().toLocaleTimeString();
            let className = type === 'success' ? 'success' : type === 'error' ? 'error' : type === 'warning' ? 'warning' : '';
            logContainer.innerHTML += `<span class="${className}">[${time}] ${message}</span>\n`;
            logContainer.scrollTop = logContainer.scrollHeight;
            console.log(`[${time}] ${message}`);
        }
        
        function clearLog() {
            logContainer.innerHTML = '[' + new Date().toLocaleTimeString() + '] 日志已清空\n';
        }
        
        function checkStatus() {
            log('🔍 开始检查状态...', 'normal');
            
            // 检测Android接口
            let hasAndroidContacts = typeof AndroidContacts !== 'undefined';
            let hasAndroidInterface = typeof AndroidInterface !== 'undefined';
            
            log('AndroidContacts接口: ' + (hasAndroidContacts ? '✅ 存在' : '❌ 不存在'), hasAndroidContacts ? 'success' : 'error');
            log('AndroidInterface接口: ' + (hasAndroidInterface ? '✅ 存在' : '❌ 不存在'), hasAndroidInterface ? 'success' : 'error');
            
            if (hasAndroidContacts && typeof AndroidContacts.checkPermissionStatus === 'function') {
                try {
                    AndroidContacts.checkPermissionStatus();
                    log('✅ 权限状态检查调用成功', 'success');
                } catch (e) {
                    log('❌ 权限状态检查失败: ' + e.message, 'error');
                }
            } else if (hasAndroidContacts && typeof AndroidContacts.getDetailedStatus === 'function') {
                try {
                    let status = AndroidContacts.getDetailedStatus();
                    statusContainer.textContent = status;
                    log('✅ 详细状态获取成功', 'success');
                } catch (e) {
                    log('❌ 详细状态获取失败: ' + e.message, 'error');
                }
            } else {
                log('❌ 没有可用的状态检查方法', 'error');
                statusContainer.textContent = '错误: 无法获取状态信息\n请确认APP正确加载了权限处理代码';
            }
        }
        
        function testDirectRequest() {
            log('🎯 开始直接权限申请测试...', 'normal');
            
            if (typeof AndroidContacts !== 'undefined' && 
                typeof AndroidContacts.directPermissionRequest === 'function') {
                try {
                    AndroidContacts.directPermissionRequest();
                    log('✅ 直接权限申请调用成功', 'success');
                    log('⏳ 请观察是否出现权限弹窗...', 'warning');
                } catch (e) {
                    log('❌ 直接权限申请失败: ' + e.message, 'error');
                }
            } else {
                log('❌ directPermissionRequest 方法不可用', 'error');
                // 尝试备用方法
                testSimpleRequest();
            }
        }
        
        function testSimpleRequest() {
            log('🔄 尝试简单权限申请...', 'normal');
            
            if (typeof AndroidContacts !== 'undefined' && 
                typeof AndroidContacts.simplePermissionTest === 'function') {
                try {
                    AndroidContacts.simplePermissionTest();
                    log('✅ 简单权限申请调用成功', 'success');
                } catch (e) {
                    log('❌ 简单权限申请失败: ' + e.message, 'error');
                }
            } else {
                log('❌ 所有权限申请方法都不可用', 'error');
            }
        }
        
        function testForceRequest() {
            log('🔥 开始强制权限测试...', 'normal');
            
            if (typeof AndroidContacts !== 'undefined' && 
                typeof AndroidContacts.forcePermissionDialog === 'function') {
                try {
                    AndroidContacts.forcePermissionDialog();
                    log('✅ 强制权限测试调用成功', 'success');
                } catch (e) {
                    log('❌ 强制权限测试失败: ' + e.message, 'error');
                }
            } else {
                log('❌ forcePermissionDialog 方法不可用', 'error');
            }
        }
        
        function testInterface() {
            log('🧪 测试JS接口连接...', 'normal');
            
            let interfaces = [];
            if (typeof AndroidContacts !== 'undefined') {
                interfaces.push('AndroidContacts');
                let methods = Object.getOwnPropertyNames(AndroidContacts);
                log('AndroidContacts方法: ' + methods.join(', '), 'normal');
            }
            if (typeof AndroidInterface !== 'undefined') {
                interfaces.push('AndroidInterface');
                let methods = Object.getOwnPropertyNames(AndroidInterface);
                log('AndroidInterface方法: ' + methods.join(', '), 'normal');
            }
            
            if (interfaces.length === 0) {
                log('❌ 没有检测到任何Android接口', 'error');
                log('可能原因: WebView配置问题或JS接口未正确注入', 'warning');
            } else {
                log('✅ 检测到接口: ' + interfaces.join(', '), 'success');
            }
        }
        
        function readContacts() {
            log('📖 尝试读取通讯录...', 'normal');
            
            if (typeof AndroidContacts !== 'undefined' && 
                typeof AndroidContacts.getContacts === 'function') {
                try {
                    let contacts = AndroidContacts.getContacts();
                    if (contacts) {
                        let contactsData = JSON.parse(contacts);
                        log('✅ 通讯录读取成功，联系人数量: ' + contactsData.length, 'success');
                        if (contactsData.length > 0) {
                            log('示例联系人: ' + contactsData[0].name, 'normal');
                        }
                    } else {
                        log('⚠️ 通讯录数据为空', 'warning');
                    }
                } catch (e) {
                    log('❌ 通讯录读取失败: ' + e.message, 'error');
                }
            } else {
                log('❌ getContacts 方法不可用', 'error');
            }
        }
        
        // 权限结果回调
        window.onContactsPermissionResult = function(granted) {
            log('🎯 权限申请结果: ' + (granted ? '✅ 已授权' : '❌ 被拒绝'), granted ? 'success' : 'error');
            if (granted) {
                log('可以尝试读取通讯录了', 'success');
            } else {
                log('权限被拒绝，无法读取通讯录', 'warning');
            }
        };
        
        // 页面加载完成自动检查
        document.addEventListener('DOMContentLoaded', function() {
            log('📄 权限调试页面加载完成', 'success');
            setTimeout(function() {
                log('🚀 自动开始状态检查...', 'normal');
                checkStatus();
            }, 500);
        });
        
        // 全局错误捕获
        window.addEventListener('error', function(e) {
            log('❌ JavaScript错误: ' + e.message, 'error');
        });
    </script>
</body>
</html>
