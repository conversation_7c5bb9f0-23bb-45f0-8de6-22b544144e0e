<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>权限调试测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            line-height: 1.6;
        }
        .btn {
            display: block;
            width: 100%;
            padding: 15px;
            margin: 10px 0;
            font-size: 16px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            background: #007cba;
            color: white;
        }
        .btn:hover {
            background: #005a8b;
        }
        .debug-btn {
            background: #28a745;
        }
        .debug-btn:hover {
            background: #218838;
        }
        .status {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            white-space: pre-line;
            font-family: monospace;
        }
        .log {
            background: #343a40;
            color: #fff;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>🔧 权限调试测试工具</h1>
    
    <button class="btn debug-btn" onclick="checkPermissionStatus()">1. 检查权限状态</button>
    <div id="status" class="status">等待检查...</div>
    
    <button class="btn debug-btn" onclick="directPermissionRequest()">2. 直接权限申请</button>
    
    <button class="btn debug-btn" onclick="forcePermissionTest()">3. 强制权限弹窗测试</button>
    
    <button class="btn debug-btn" onclick="testJSInterface()">4. 测试JS接口连接</button>
    
    <button class="btn" onclick="originalPermissionTest()">5. 原始权限申请测试</button>
    
    <button class="btn" onclick="readContacts()">6. 直接读取通讯录</button>
    
    <button class="btn debug-btn" onclick="forceReadContacts()">7. 强制读取通讯录 (新增)</button>
    
    <button class="btn debug-btn" onclick="testContactsAccess()">8. 完整通讯录测试</button>
    
    <h3>🔍 实时日志</h3>
    <div id="log" class="log">等待日志...</div>
    
    <button class="btn debug-btn" onclick="clearLog()">清空日志</button>

    <script>
        let logContainer = document.getElementById('log');
        let statusContainer = document.getElementById('status');
        
        function log(message) {
            let time = new Date().toLocaleTimeString();
            logContainer.innerHTML += `[${time}] ${message}\n`;
            logContainer.scrollTop = logContainer.scrollHeight;
            console.log(message);
        }
        
        function clearLog() {
            logContainer.innerHTML = '日志已清空\n';
        }
        
        function checkPermissionStatus() {
            log('🔍 检查权限状态...');
            
            if (typeof AndroidContacts !== 'undefined' && 
                typeof AndroidContacts.checkPermissionStatus === 'function') {
                try {
                    AndroidContacts.checkPermissionStatus();
                    log('✅ 权限状态检查调用成功');
                } catch (e) {
                    log('❌ 权限状态检查失败: ' + e.message);
                }
            } else {
                log('❌ checkPermissionStatus 方法不可用');
            }
        }
        
        function directPermissionRequest() {
            log('🎯 开始直接权限申请...');
            
            if (typeof AndroidContacts !== 'undefined' && 
                typeof AndroidContacts.directPermissionRequest === 'function') {
                try {
                    AndroidContacts.directPermissionRequest();
                    log('✅ 直接权限申请调用成功');
                } catch (e) {
                    log('❌ 直接权限申请失败: ' + e.message);
                }
            } else {
                log('❌ directPermissionRequest 方法不可用');
            }
        }
        
        function getStatus() {
            log('🔍 开始获取状态...');
            
            // 检测接口
            if (typeof AndroidContacts !== 'undefined') {
                log('✅ AndroidContacts 接口可用');
                if (typeof AndroidContacts.getDetailedStatus === 'function') {
                    try {
                        let status = AndroidContacts.getDetailedStatus();
                        statusContainer.textContent = status;
                        log('✅ 状态获取成功');
                    } catch (e) {
                        log('❌ 状态获取失败: ' + e.message);
                    }
                } else {
                    log('❌ getDetailedStatus 方法不存在');
                }
            } else if (typeof AndroidInterface !== 'undefined') {
                log('⚠️ 只有 AndroidInterface 可用');
                statusContainer.textContent = 'AndroidInterface 可用，但缺少 getDetailedStatus 方法';
            } else {
                log('❌ 没有可用的Android接口');
                statusContainer.textContent = '错误: 没有检测到任何Android接口';
            }
        }
        
        function forcePermissionTest() {
            log('🔥 开始强制权限测试...');
            
            if (typeof AndroidContacts !== 'undefined' && 
                typeof AndroidContacts.forcePermissionDialog === 'function') {
                try {
                    AndroidContacts.forcePermissionDialog();
                    log('✅ 强制权限测试调用成功');
                } catch (e) {
                    log('❌ 强制权限测试失败: ' + e.message);
                }
            } else {
                log('❌ forcePermissionDialog 方法不可用');
            }
        }
        
        function testJSInterface() {
            log('🧪 测试JS接口连接...');
            
            let hasAndroidContacts = typeof AndroidContacts !== 'undefined';
            let hasAndroidInterface = typeof AndroidInterface !== 'undefined';
            
            log('AndroidContacts 存在: ' + hasAndroidContacts);
            log('AndroidInterface 存在: ' + hasAndroidInterface);
            
            if (hasAndroidContacts) {
                let methods = Object.getOwnPropertyNames(AndroidContacts);
                log('AndroidContacts 方法: ' + methods.join(', '));
            }
            
            if (hasAndroidInterface) {
                let methods = Object.getOwnPropertyNames(AndroidInterface);
                log('AndroidInterface 方法: ' + methods.join(', '));
            }
        }
        
        function originalPermissionTest() {
            log('📱 开始原始权限申请...');
            
            if (typeof AndroidContacts !== 'undefined' && 
                typeof AndroidContacts.simplePermissionTest === 'function') {
                try {
                    AndroidContacts.simplePermissionTest();
                    log('✅ 原始权限申请调用成功');
                } catch (e) {
                    log('❌ 原始权限申请失败: ' + e.message);
                }
            } else {
                log('❌ simplePermissionTest 方法不可用');
            }
        }
        
        function readContacts() {
            log('📖 尝试直接读取通讯录...');
            
            if (typeof AndroidContacts !== 'undefined' && 
                typeof AndroidContacts.getContacts === 'function') {
                try {
                    let contacts = AndroidContacts.getContacts();
                    log('✅ 通讯录读取成功，数量: ' + (contacts ? JSON.parse(contacts).length : 0));
                } catch (e) {
                    log('❌ 通讯录读取失败: ' + e.message);
                }
            } else {
                log('❌ getContacts 方法不可用');
            }
        }
        
        function forceReadContacts() {
            log('🔥 开始强制读取通讯录...');
            
            if (typeof AndroidContacts !== 'undefined' && 
                typeof AndroidContacts.forceReadContacts === 'function') {
                try {
                    AndroidContacts.forceReadContacts();
                    log('✅ 强制读取调用成功，等待结果...');
                } catch (e) {
                    log('❌ 强制读取失败: ' + e.message);
                }
            } else {
                log('❌ forceReadContacts 方法不可用');
            }
        }
        
        function testContactsAccess() {
            log('🧪 开始完整通讯录访问测试...');
            
            // 步骤1：检查权限
            log('步骤1: 检查权限状态');
            if (typeof AndroidContacts !== 'undefined' && 
                typeof AndroidContacts.hasContactsPermission === 'function') {
                try {
                    let hasPermission = AndroidContacts.hasContactsPermission();
                    log('权限状态: ' + (hasPermission ? '✅ 已授权' : '❌ 未授权'));
                    
                    if (!hasPermission) {
                        log('权限未授权，终止测试');
                        return;
                    }
                } catch (e) {
                    log('权限检查失败: ' + e.message);
                    return;
                }
            } else {
                log('权限检查方法不可用');
                return;
            }
            
            // 步骤2：同步读取
            log('步骤2: 同步读取通讯录');
            if (typeof AndroidContacts !== 'undefined' && 
                typeof AndroidContacts.getContacts === 'function') {
                try {
                    let contacts = AndroidContacts.getContacts();
                    let parsedContacts = JSON.parse(contacts || '[]');
                    log('同步读取成功，数量: ' + parsedContacts.length);
                    
                    if (parsedContacts.length > 0) {
                        log('第一个联系人: ' + JSON.stringify(parsedContacts[0]));
                    }
                } catch (e) {
                    log('同步读取失败: ' + e.message);
                }
            }
            
            // 步骤3：异步读取
            log('步骤3: 异步强制读取');
            forceReadContacts();
        }
        
        // 设置权限回调
        window.onContactsPermissionResult = function(granted) {
            log('🎯 权限回调: ' + (granted ? '已授权' : '被拒绝'));
        };
        
        // 设置通讯录数据回调 - 增强版
        window.onContactsResult = function(contactsJson) {
            log('📞 收到通讯录数据回调');
            log('数据类型: ' + typeof contactsJson);
            log('数据长度: ' + (contactsJson ? contactsJson.length : 0));
            
            try {
                let contacts = JSON.parse(contactsJson || '[]');
                log('✅ 解析成功，联系人数量: ' + contacts.length);
                
                if (contacts.length > 0) {
                    log('第一个联系人示例: ' + JSON.stringify(contacts[0]));
                    
                    // 显示前5个联系人的信息
                    let preview = contacts.slice(0, 5).map(c => c.name + ': ' + c.phone).join(', ');
                    log('前5个联系人: ' + preview);
                }
                
                // 保存到全局变量
                window.contactsData = contacts;
                log('数据已保存到 window.contactsData');
                
            } catch (e) {
                log('❌ 数据解析失败: ' + e.message);
                log('原始数据: ' + (contactsJson || 'null').substring(0, 200));
            }
        };
        
        // 页面加载完成
        document.addEventListener('DOMContentLoaded', function() {
            log('📄 调试页面加载完成');
            setTimeout(checkPermissionStatus, 1000);
        });
    </script>
</body>
</html>
