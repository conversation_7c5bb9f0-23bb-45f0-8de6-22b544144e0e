<libraries>
  <library
      name="com.google.android.material:material:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\7cfde09bef56db81a739e41054cc6284\transformed\material-1.7.0\jars\classes.jar"
      resolved="com.google.android.material:material:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\7cfde09bef56db81a739e41054cc6284\transformed\material-1.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat:1.5.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\ae146d59d58c77d62f24ab479ef1acb9\transformed\appcompat-1.5.1\jars\classes.jar"
      resolved="androidx.appcompat:appcompat:1.5.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\ae146d59d58c77d62f24ab479ef1acb9\transformed\appcompat-1.5.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.constraintlayout:constraintlayout:2.1.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\7841b0cd93ac26a6703685554707ae4d\transformed\constraintlayout-2.1.4\jars\classes.jar"
      resolved="androidx.constraintlayout:constraintlayout:2.1.4"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\7841b0cd93ac26a6703685554707ae4d\transformed\constraintlayout-2.1.4"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager2:viewpager2:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\556fc0646425edbc97aa43751507d932\transformed\jetified-viewpager2-1.0.0\jars\classes.jar"
      resolved="androidx.viewpager2:viewpager2:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\556fc0646425edbc97aa43751507d932\transformed\jetified-viewpager2-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.fragment:fragment:1.3.6@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\c0f328dba3cc25af5f7c61e5975fd1b4\transformed\fragment-1.3.6\jars\classes.jar"
      resolved="androidx.fragment:fragment:1.3.6"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\c0f328dba3cc25af5f7c61e5975fd1b4\transformed\fragment-1.3.6"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity:1.5.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\fe943357b5d338bb6d7c3bbeb1ed454c\transformed\jetified-activity-1.5.1\jars\classes.jar"
      resolved="androidx.activity:activity:1.5.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\fe943357b5d338bb6d7c3bbeb1ed454c\transformed\jetified-activity-1.5.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat-resources:1.5.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\657253e1c0cb430f84bc31258399c250\transformed\jetified-appcompat-resources-1.5.1\jars\classes.jar"
      resolved="androidx.appcompat:appcompat-resources:1.5.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\657253e1c0cb430f84bc31258399c250\transformed\jetified-appcompat-resources-1.5.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.drawerlayout:drawerlayout:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\35c215daf508ea2ed905f6c95eecfcdb\transformed\drawerlayout-1.1.1\jars\classes.jar"
      resolved="androidx.drawerlayout:drawerlayout:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\35c215daf508ea2ed905f6c95eecfcdb\transformed\drawerlayout-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\72de7e762df7c43ffd01ce059f3b8139\transformed\coordinatorlayout-1.1.0\jars\classes.jar"
      resolved="androidx.coordinatorlayout:coordinatorlayout:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\72de7e762df7c43ffd01ce059f3b8139\transformed\coordinatorlayout-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.dynamicanimation:dynamicanimation:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\b64eec0b7a88160a6e6bb64fa31568da\transformed\dynamicanimation-1.0.0\jars\classes.jar"
      resolved="androidx.dynamicanimation:dynamicanimation:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\b64eec0b7a88160a6e6bb64fa31568da\transformed\dynamicanimation-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.recyclerview:recyclerview:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\62c7b45c02a319d21e2673c065692f06\transformed\recyclerview-1.1.0\jars\classes.jar"
      resolved="androidx.recyclerview:recyclerview:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\62c7b45c02a319d21e2673c065692f06\transformed\recyclerview-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.transition:transition:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\11c07b0be527dbc89e7fba9344b85062\transformed\transition-1.2.0\jars\classes.jar"
      resolved="androidx.transition:transition:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\11c07b0be527dbc89e7fba9344b85062\transformed\transition-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\8bba52d669908ffe7e363df39f09f71e\transformed\vectordrawable-animated-1.1.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable-animated:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\8bba52d669908ffe7e363df39f09f71e\transformed\vectordrawable-animated-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\82fdf85b667d965d5f03e68c8b987206\transformed\vectordrawable-1.1.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\82fdf85b667d965d5f03e68c8b987206\transformed\vectordrawable-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager:viewpager:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\73c962a0f34da5a2fd429ea13553d975\transformed\viewpager-1.0.0\jars\classes.jar"
      resolved="androidx.viewpager:viewpager:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\73c962a0f34da5a2fd429ea13553d975\transformed\viewpager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.customview:customview:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\c47e73e3e1aba521d00aa8b48fedcb3e\transformed\customview-1.1.0\jars\classes.jar"
      resolved="androidx.customview:customview:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\c47e73e3e1aba521d00aa8b48fedcb3e\transformed\customview-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\13e0e57dce109f1b01710a3644826884\transformed\legacy-support-core-utils-1.0.0\jars\classes.jar"
      resolved="androidx.legacy:legacy-support-core-utils:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\13e0e57dce109f1b01710a3644826884\transformed\legacy-support-core-utils-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.loader:loader:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\fcfff1cb6db275c65d5464f32efac566\transformed\loader-1.0.0\jars\classes.jar"
      resolved="androidx.loader:loader:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\fcfff1cb6db275c65d5464f32efac566\transformed\loader-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\fe1ff209f9eaf6183c9fb79d26a573da\transformed\jetified-lifecycle-viewmodel-savedstate-2.5.1\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\fe1ff209f9eaf6183c9fb79d26a573da\transformed\jetified-lifecycle-viewmodel-savedstate-2.5.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core-ktx:1.8.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\f712ab45743b1508a2467acccb7632d0\transformed\jetified-core-ktx-1.8.0\jars\classes.jar"
      resolved="androidx.core:core-ktx:1.8.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\f712ab45743b1508a2467acccb7632d0\transformed\jetified-core-ktx-1.8.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core:1.8.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\800f0ebdbf82eee61967fbab7276b7a0\transformed\core-1.8.0\jars\classes.jar"
      resolved="androidx.core:core:1.8.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\800f0ebdbf82eee61967fbab7276b7a0\transformed\core-1.8.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\af66ada97217545d02d6c228a333a6e7\transformed\cursoradapter-1.0.0\jars\classes.jar"
      resolved="androidx.cursoradapter:cursoradapter:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\af66ada97217545d02d6c228a333a6e7\transformed\cursoradapter-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\8aa4623d3dab8fda6af309c5b40d4918\transformed\jetified-savedstate-1.2.0\jars\classes.jar"
      resolved="androidx.savedstate:savedstate:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\8aa4623d3dab8fda6af309c5b40d4918\transformed\jetified-savedstate-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.cardview:cardview:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\01545e93efa0c031c7de57f8850972a2\transformed\cardview-1.0.0\jars\classes.jar"
      resolved="androidx.cardview:cardview:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\01545e93efa0c031c7de57f8850972a2\transformed\cardview-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime:2.5.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\11449791ce6229a4d8820c0b2ab93a1a\transformed\lifecycle-runtime-2.5.1\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime:2.5.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\11449791ce6229a4d8820c0b2ab93a1a\transformed\lifecycle-runtime-2.5.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel:2.5.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\3684c3db430ccf26f9b76677b2abf25b\transformed\lifecycle-viewmodel-2.5.1\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel:2.5.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\3684c3db430ccf26f9b76677b2abf25b\transformed\lifecycle-viewmodel-2.5.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\05c292596d671412479aee05f01fdb8d\transformed\versionedparcelable-1.1.1\jars\classes.jar"
      resolved="androidx.versionedparcelable:versionedparcelable:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\05c292596d671412479aee05f01fdb8d\transformed\versionedparcelable-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.collection:collection:1.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.collection\collection\1.1.0\1f27220b47669781457de0d600849a5de0e89909\collection-1.1.0.jar"
      resolved="androidx.collection:collection:1.1.0"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata:2.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\c233f723b02721cbc1d9cd693cf18ccd\transformed\lifecycle-livedata-2.0.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata:2.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\c233f723b02721cbc1d9cd693cf18ccd\transformed\lifecycle-livedata-2.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-runtime:2.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\56bb13959964d8b2e1a7e27387dd8ebb\transformed\core-runtime-2.1.0\jars\classes.jar"
      resolved="androidx.arch.core:core-runtime:2.1.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\56bb13959964d8b2e1a7e27387dd8ebb\transformed\core-runtime-2.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-common:2.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.arch.core\core-common\2.1.0\b3152fc64428c9354344bd89848ecddc09b6f07e\core-common-2.1.0.jar"
      resolved="androidx.arch.core:core-common:2.1.0"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core:2.5.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\487618fd01e472d4196d5a6341fe4fba\transformed\lifecycle-livedata-core-2.5.1\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core:2.5.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\487618fd01e472d4196d5a6341fe4fba\transformed\lifecycle-livedata-core-2.5.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-common:2.5.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.lifecycle\lifecycle-common\2.5.1\1fdb7349701e9cf2f0a69fc10642b6fef6bb3e12\lifecycle-common-2.5.1.jar"
      resolved="androidx.lifecycle:lifecycle-common:2.5.1"/>
  <library
      name="androidx.interpolator:interpolator:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\31b3bde0a14d92a895a4904f71ecd408\transformed\interpolator-1.0.0\jars\classes.jar"
      resolved="androidx.interpolator:interpolator:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\31b3bde0a14d92a895a4904f71ecd408\transformed\interpolator-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.documentfile:documentfile:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\62b6761725fc2b2b74203d67536ca32d\transformed\documentfile-1.0.0\jars\classes.jar"
      resolved="androidx.documentfile:documentfile:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\62b6761725fc2b2b74203d67536ca32d\transformed\documentfile-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\08fcff8dca94f664fb9e64d8b790c8ba\transformed\localbroadcastmanager-1.0.0\jars\classes.jar"
      resolved="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\08fcff8dca94f664fb9e64d8b790c8ba\transformed\localbroadcastmanager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.print:print:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\6c8119ca7343f5eb9709d91bbcc43e73\transformed\print-1.0.0\jars\classes.jar"
      resolved="androidx.print:print:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\6c8119ca7343f5eb9709d91bbcc43e73\transformed\print-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.annotation:annotation:1.3.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.annotation\annotation\1.3.0\21f49f5f9b85fc49de712539f79123119740595\annotation-1.3.0.jar"
      resolved="androidx.annotation:annotation:1.3.0"/>
  <library
      name="androidx.annotation:annotation-experimental:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\27d1ee9f31d83f6b693a1bf75bd9da6c\transformed\jetified-annotation-experimental-1.1.0\jars\classes.jar"
      resolved="androidx.annotation:annotation-experimental:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\27d1ee9f31d83f6b693a1bf75bd9da6c\transformed\jetified-annotation-experimental-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.6.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-core-jvm\1.6.1\97fd74ccf54a863d221956ffcd21835e168e2aaa\kotlinx-coroutines-core-jvm-1.6.1.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.6.1"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-android\1.6.1\4e61fcdcc508cbaa37c4a284a50205d7c7767e37\kotlinx-coroutines-android-1.6.1.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.1"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.6.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk8\1.6.0\baf82c475e9372c25407f3d132439e4aa803b8b8\kotlin-stdlib-jdk8-1.6.0.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.6.0"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.6.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk7\1.6.0\da6bdc87391322974a43ccc00a25536ae74dad51\kotlin-stdlib-jdk7-1.6.0.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.6.0"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib:1.7.10@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib\1.7.10\d2abf9e77736acc4450dc4a3f707fa2c10f5099d\kotlin-stdlib-1.7.10.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib:1.7.10"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-common:1.7.10@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-common\1.7.10\bac80c520d0a9e3f3673bc2658c6ed02ef45a76a\kotlin-stdlib-common-1.7.10.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-common:1.7.10"/>
  <library
      name="org.jetbrains:annotations:13.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains\annotations\13.0\919f0dfe192fb4e063e7dacadee7f8bb9a2672a9\annotations-13.0.jar"
      resolved="org.jetbrains:annotations:13.0"/>
  <library
      name="androidx.emoji2:emoji2-views-helper:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\fb2ecef6d4136617693791a86ff4c84a\transformed\jetified-emoji2-views-helper-1.2.0\jars\classes.jar"
      resolved="androidx.emoji2:emoji2-views-helper:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\fb2ecef6d4136617693791a86ff4c84a\transformed\jetified-emoji2-views-helper-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.emoji2:emoji2:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\f8c9f96e058eeab992770628d0528359\transformed\jetified-emoji2-1.2.0\jars\classes.jar;C:\Users\<USER>\.gradle\caches\transforms-3\f8c9f96e058eeab992770628d0528359\transformed\jetified-emoji2-1.2.0\jars\libs\repackaged.jar"
      resolved="androidx.emoji2:emoji2:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\f8c9f96e058eeab992770628d0528359\transformed\jetified-emoji2-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-process:2.4.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\27047c5d0bb513344ae4ceefe258867b\transformed\jetified-lifecycle-process-2.4.1\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-process:2.4.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\27047c5d0bb513344ae4ceefe258867b\transformed\jetified-lifecycle-process-2.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.resourceinspection\resourceinspection-annotation\1.0.1\8c21f8ff5d96d5d52c948707f7e4d6ca6773feef\resourceinspection-annotation-1.0.1.jar"
      resolved="androidx.resourceinspection:resourceinspection-annotation:1.0.1"/>
  <library
      name="androidx.startup:startup-runtime:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\81dd96ad7475982a690b5b46895f63bc\transformed\jetified-startup-runtime-1.1.1\jars\classes.jar"
      resolved="androidx.startup:startup-runtime:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\81dd96ad7475982a690b5b46895f63bc\transformed\jetified-startup-runtime-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.tracing:tracing:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\879d949361e78a57a6a7aae7b75cd304\transformed\jetified-tracing-1.0.0\jars\classes.jar"
      resolved="androidx.tracing:tracing:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\879d949361e78a57a6a7aae7b75cd304\transformed\jetified-tracing-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.concurrent:concurrent-futures:1.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.concurrent\concurrent-futures\1.0.0\c1e77e3ee6f4643b77496a1ddf7a2eef1aefdaa1\concurrent-futures-1.0.0.jar"
      resolved="androidx.concurrent:concurrent-futures:1.0.0"/>
  <library
      name="androidx.constraintlayout:constraintlayout-core:1.0.4@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.constraintlayout\constraintlayout-core\1.0.4\29cdbe03ded6b0980f63fa5da2579a430e911c40\constraintlayout-core-1.0.4.jar"
      resolved="androidx.constraintlayout:constraintlayout-core:1.0.4"/>
  <library
      name="com.google.guava:listenablefuture:1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.guava\listenablefuture\1.0\c949a840a6acbc5268d088e47b04177bf90b3cad\listenablefuture-1.0.jar"
      resolved="com.google.guava:listenablefuture:1.0"/>
</libraries>
