/**
 * 随意花APP通讯录桥接接口
 * 提供Web页面与Android原生通讯录交互功能
 */

(function() {
    'use strict';
    
    // 检查是否在APP环境中
    const isInApp = typeof AndroidContacts !== 'undefined';
    
    if (!isInApp) {
        console.log('🌐 当前在浏览器环境，通讯录功能不可用');
        return;
    }
    
    console.log('📱 随意花APP通讯录接口已加载');
    
    // 通讯录管理器
    window.ContactsManager = {
        
        /**
         * 读取所有通讯录
         * @param {Function} callback 成功回调函数
         * @param {Function} errorCallback 错误回调函数
         */
        readAll: function(callback, errorCallback) {
            console.log('🔍 开始读取通讯录...');
            
            // 设置回调
            window.onContactsResult = function(contactsJson) {
                try {
                    const contacts = JSON.parse(contactsJson);
                    console.log('📞 成功读取', contacts.length, '条通讯录');
                    
                    if (typeof callback === 'function') {
                        callback(contacts);
                    }
                    
                    // 触发全局事件
                    document.dispatchEvent(new CustomEvent('contactsLoaded', { 
                        detail: contacts 
                    }));
                    
                } catch(e) {
                    console.error('❌ 解析通讯录数据失败:', e);
                    if (typeof errorCallback === 'function') {
                        errorCallback(e);
                    }
                }
            };
            
            // 请求通讯录
            AndroidContacts.requestContacts();
        },
        
        /**
         * 检查通讯录权限
         * @param {Function} callback 权限状态回调
         */
        checkPermission: function(callback) {
            console.log('🔐 检查通讯录权限...');
            
            window.onContactsPermissionResult = function(hasPermission) {
                console.log('权限状态:', hasPermission ? '已授权' : '未授权');
                
                if (typeof callback === 'function') {
                    callback(hasPermission);
                }
            };
            
            AndroidContacts.checkPermission();
        },
        
        /**
         * 格式化通讯录数据
         * @param {Array} contacts 原始通讯录数组
         * @returns {Array} 格式化后的通讯录
         */
        formatContacts: function(contacts) {
            return contacts.map(contact => ({
                name: contact.name || '未知',
                phone: this.formatPhone(contact.phone || ''),
                firstLetter: this.getFirstLetter(contact.name || '')
            }));
        },
        
        /**
         * 格式化手机号
         */
        formatPhone: function(phone) {
            return phone.replace(/[^\d]/g, '');
        },
        
        /**
         * 获取姓名首字母
         */
        getFirstLetter: function(name) {
            if (!name) return '#';
            const firstChar = name.charAt(0);
            return /[a-zA-Z]/.test(firstChar) ? firstChar.toUpperCase() : '#';
        },
        
        /**
         * 搜索通讯录
         * @param {Array} contacts 通讯录数组
         * @param {String} keyword 搜索关键词
         */
        search: function(contacts, keyword) {
            if (!keyword) return contacts;
            
            keyword = keyword.toLowerCase();
            return contacts.filter(contact => 
                contact.name.toLowerCase().includes(keyword) ||
                contact.phone.includes(keyword)
            );
        }
    };
    
    // 兼容性方法（保持向后兼容）
    window.readAllContacts = function() {
        ContactsManager.readAll();
    };
    
    window.checkContactsPermission = function() {
        ContactsManager.checkPermission();
    };
    
    // 便捷的Promise封装
    window.getContacts = function() {
        return new Promise((resolve, reject) => {
            ContactsManager.readAll(resolve, reject);
        });
    };
    
    window.hasContactsPermission = function() {
        return new Promise((resolve) => {
            ContactsManager.checkPermission(resolve);
        });
    };
    
    console.log('✅ 通讯录接口初始化完成');
    console.log('使用方法:');
    console.log('  ContactsManager.readAll(callback)');
    console.log('  ContactsManager.checkPermission(callback)');
    console.log('  getContacts().then(contacts => ...)');
    
})();
