package com.dailuanshej.loan;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.webkit.WebSettings;
import android.widget.Toast;

/**
 * 合同页面Activity - 专门用于显示合同
 * 针对移动端和APK环境进行了优化
 */
public class ContractActivity extends Activity {
    private static final String TAG = "ContractActivity";
    private WebView contractWebView;
    private String contractUrl;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        Log.d(TAG, "ContractActivity 开始创建");

        // 设置布局
        setContentView(R.layout.activity_contract);

        // 获取传入的合同URL
        Intent intent = getIntent();
        contractUrl = intent.getStringExtra("contract_url");

        if (contractUrl == null || contractUrl.isEmpty()) {
            // 默认合同URL
            contractUrl = "https://dailuanshej.cn/customer_contract_mobile.php?id=1";
        }

        // 确保使用移动端版本
        if (contractUrl.contains("customer_contract.php") && !contractUrl.contains("customer_contract_mobile.php")) {
            contractUrl = contractUrl.replace("customer_contract.php", "customer_contract_mobile.php");
        }

        Log.d(TAG, "合同URL: " + contractUrl);

        initViews();
        initWebView();
        loadContract();
    }

    private void initViews() {
        // 初始化视图组件
        findViewById(R.id.btn_back).setOnClickListener(v -> finish());
        findViewById(R.id.btn_menu).setOnClickListener(v -> showMenu());
        findViewById(R.id.btn_retry).setOnClickListener(v -> loadContract());

        // 底部按钮（如果需要的话）
        findViewById(R.id.btn_print).setOnClickListener(v -> printContract());
        findViewById(R.id.btn_save).setOnClickListener(v -> saveContract());
    }
    
    private void initWebView() {
        // 创建WebView并添加到容器中
        contractWebView = new WebView(this);
        android.widget.FrameLayout webViewContainer = findViewById(R.id.webview_container);
        webViewContainer.addView(contractWebView);
        
        // 配置WebView设置
        WebSettings webSettings = contractWebView.getSettings();
        webSettings.setJavaScriptEnabled(true);
        webSettings.setDomStorageEnabled(true);
        webSettings.setLoadWithOverviewMode(true);
        webSettings.setUseWideViewPort(true);
        webSettings.setBuiltInZoomControls(false);
        webSettings.setDisplayZoomControls(false);
        webSettings.setSupportZoom(false);
        webSettings.setTextZoom(100);
        
        // 针对合同页面的特殊设置
        webSettings.setLayoutAlgorithm(WebSettings.LayoutAlgorithm.TEXT_AUTOSIZING);
        webSettings.setLoadWithOverviewMode(true);
        webSettings.setUseWideViewPort(true);
        
        // 设置User-Agent，标识为APK环境
        String userAgent = webSettings.getUserAgentString();
        webSettings.setUserAgentString(userAgent + " DaiLuanSheJAPK/1.0 WebView");
        
        Log.d(TAG, "WebView配置完成，User-Agent: " + webSettings.getUserAgentString());
        
        // 设置WebViewClient
        contractWebView.setWebViewClient(new WebViewClient() {
            @Override
            public boolean shouldOverrideUrlLoading(WebView view, String url) {
                Log.d(TAG, "合同页面URL加载: " + url);
                
                // 确保合同相关页面使用移动端版本
                if (url.contains("customer_contract.php") && !url.contains("customer_contract_mobile.php")) {
                    String mobileUrl = url.replace("customer_contract.php", "customer_contract_mobile.php");
                    Log.d(TAG, "重定向到移动端合同: " + mobileUrl);
                    view.loadUrl(mobileUrl);
                    return true;
                }
                
                view.loadUrl(url);
                return true;
            }
            
            @Override
            public void onPageStarted(WebView view, String url, android.graphics.Bitmap favicon) {
                super.onPageStarted(view, url, favicon);
                Log.d(TAG, "合同页面开始加载: " + url);
                showLoading();
            }
            
            @Override
            public void onPageFinished(WebView view, String url) {
                super.onPageFinished(view, url);
                Log.d(TAG, "合同页面加载完成: " + url);

                hideLoading();
                showContent();

                // 注入合同优化脚本
                injectContractOptimizations();
            }
            
            @Override
            public void onReceivedError(WebView view, int errorCode, String description, String failingUrl) {
                super.onReceivedError(view, errorCode, description, failingUrl);
                Log.e(TAG, "合同页面加载错误: " + description);
                showError(description);
            }
        });
    }
    
    private void loadContract() {
        Log.d(TAG, "开始加载合同: " + contractUrl);
        contractWebView.loadUrl(contractUrl);
    }
    
    private void injectContractOptimizations() {
        Log.d(TAG, "注入合同页面优化脚本");
        
        String optimizationScript = 
            "console.log('📱 ContractActivity 合同优化脚本开始');" +
            
            // 标记APK环境
            "window.isAPKEnvironment = true;" +
            "window.isContractActivity = true;" +
            
            // 合同页面优化
            "function optimizeContractDisplay() {" +
            "    console.log('🔧 开始优化合同显示');" +
            "    " +
            "    // 禁用页面缩放" +
            "    var viewport = document.querySelector('meta[name=viewport]');" +
            "    if (viewport) {" +
            "        viewport.setAttribute('content', 'width=device-width, initial-scale=1.0, user-scalable=no');" +
            "    }" +
            "    " +
            "    // 添加APK专用样式" +
            "    var style = document.createElement('style');" +
            "    style.textContent = `" +
            "        body { " +
            "            -webkit-text-size-adjust: 100% !important; " +
            "            overflow-x: hidden !important; " +
            "            font-size: 14px !important; " +
            "            writing-mode: horizontal-tb !important; " +
            "            text-orientation: mixed !important; " +
            "            direction: ltr !important; " +
            "        }" +
            "        *, *::before, *::after { " +
            "            writing-mode: horizontal-tb !important; " +
            "            text-orientation: mixed !important; " +
            "            direction: ltr !important; " +
            "        }" +
            "        .container, .content-wrapper { " +
            "            padding: 10px !important; " +
            "            margin: 0 !important; " +
            "        }" +
            "        .mobile-header { " +
            "            position: sticky !important; " +
            "            top: 0 !important; " +
            "            z-index: 1000 !important; " +
            "        }" +
            "        .mobile-actions { " +
            "            position: fixed !important; " +
            "            bottom: 0 !important; " +
            "            left: 0 !important; " +
            "            right: 0 !important; " +
            "        }" +
            "        .signature-area { " +
            "            margin-bottom: 80px !important; " +
            "        }" +
            "        img, svg, canvas { " +
            "            max-width: 100% !important; " +
            "            height: auto !important; " +
            "        }" +
            "    `;" +
            "    document.head.appendChild(style);" +
            "    console.log('✅ APK专用样式已应用');" +
            "}" +
            
            // 合同信息提取
            "function extractContractInfo() {" +
            "    try {" +
            "        var info = {};" +
            "        " +
            "        var title = document.querySelector('.contract-title, .mobile-header h1');" +
            "        if (title) info.title = title.textContent.trim();" +
            "        " +
            "        var contractNo = document.querySelector('.contract-no, .subtitle');" +
            "        if (contractNo) info.contractNo = contractNo.textContent.trim();" +
            "        " +
            "        var customerName = document.querySelector('.signature-line');" +
            "        if (customerName) info.customerName = customerName.textContent.trim();" +
            "        " +
            "        var loanAmount = document.querySelector('.amount-highlight');" +
            "        if (loanAmount) info.loanAmount = loanAmount.textContent.trim();" +
            "        " +
            "        console.log('📋 提取的合同信息:', info);" +
            "        window.contractInfo = info;" +
            "        return info;" +
            "    } catch(e) {" +
            "        console.error('提取合同信息失败:', e);" +
            "        return {};" +
            "    }" +
            "}" +
            
            // 执行优化
            "setTimeout(function() {" +
            "    optimizeContractDisplay();" +
            "    extractContractInfo();" +
            "}, 500);" +
            
            "console.log('✅ ContractActivity 合同优化脚本完成');";
            
        contractWebView.evaluateJavascript(optimizationScript, result -> {
            Log.d(TAG, "合同优化脚本执行完成");
        });
    }
    
    @Override
    public void onBackPressed() {
        if (contractWebView.canGoBack()) {
            contractWebView.goBack();
        } else {
            super.onBackPressed();
        }
    }
    
    @Override
    protected void onDestroy() {
        if (contractWebView != null) {
            contractWebView.destroy();
        }
        super.onDestroy();
    }
    
    // 静态方法：启动合同Activity
    public static void startContractActivity(Activity context, String contractUrl) {
        Intent intent = new Intent(context, ContractActivity.class);
        intent.putExtra("contract_url", contractUrl);
        context.startActivity(intent);
    }
    
    // 静态方法：启动默认合同
    public static void startDefaultContract(Activity context, int customerId) {
        String url = "https://dailuanshej.cn/customer_contract_mobile.php?id=" + customerId;
        startContractActivity(context, url);
    }

    // ========== UI状态管理方法 ==========

    private void showLoading() {
        findViewById(R.id.loading_layout).setVisibility(android.view.View.VISIBLE);
        findViewById(R.id.error_layout).setVisibility(android.view.View.GONE);
        if (contractWebView != null) {
            contractWebView.setVisibility(android.view.View.GONE);
        }
    }

    private void hideLoading() {
        findViewById(R.id.loading_layout).setVisibility(android.view.View.GONE);
    }

    private void showContent() {
        if (contractWebView != null) {
            contractWebView.setVisibility(android.view.View.VISIBLE);
        }
        findViewById(R.id.error_layout).setVisibility(android.view.View.GONE);
    }

    private void showError(String errorMessage) {
        hideLoading();
        if (contractWebView != null) {
            contractWebView.setVisibility(android.view.View.GONE);
        }

        findViewById(R.id.error_layout).setVisibility(android.view.View.VISIBLE);
        android.widget.TextView errorTextView = findViewById(R.id.tv_error_message);
        errorTextView.setText(errorMessage);
    }

    // ========== 菜单和操作方法 ==========

    private void showMenu() {
        // 创建弹出菜单
        android.widget.PopupMenu popup = new android.widget.PopupMenu(this, findViewById(R.id.btn_menu));

        // 手动添加菜单项
        popup.getMenu().add(0, 1, 0, "刷新页面");
        popup.getMenu().add(0, 2, 0, "分享合同");
        popup.getMenu().add(0, 3, 0, "关于");

        popup.setOnMenuItemClickListener(item -> {
            switch (item.getItemId()) {
                case 1: // 刷新
                    loadContract();
                    return true;
                case 2: // 分享
                    shareContract();
                    return true;
                case 3: // 关于
                    showAbout();
                    return true;
                default:
                    return false;
            }
        });

        popup.show();
    }

    private void printContract() {
        if (contractWebView != null) {
            // 调用WebView的打印功能
            android.print.PrintManager printManager = (android.print.PrintManager) getSystemService(PRINT_SERVICE);
            android.print.PrintDocumentAdapter printAdapter = contractWebView.createPrintDocumentAdapter("合同文档");
            printManager.print("借款合同", printAdapter, new android.print.PrintAttributes.Builder().build());
            Log.d(TAG, "启动打印功能");
        }
    }

    private void saveContract() {
        // 这里可以实现保存合同到本地的功能
        Toast.makeText(this, "保存功能开发中...", Toast.LENGTH_SHORT).show();
        Log.d(TAG, "保存合同功能调用");
    }

    private void shareContract() {
        Intent shareIntent = new Intent(Intent.ACTION_SEND);
        shareIntent.setType("text/plain");
        shareIntent.putExtra(Intent.EXTRA_TEXT, "借款合同链接: " + contractUrl);
        shareIntent.putExtra(Intent.EXTRA_SUBJECT, "借款合同");
        startActivity(Intent.createChooser(shareIntent, "分享合同"));
        Log.d(TAG, "分享合同");
    }

    private void showAbout() {
        new android.app.AlertDialog.Builder(this)
            .setTitle("关于")
            .setMessage("随意花贷款APP\n版本: 1.0\n\n专业的小额贷款服务平台")
            .setPositiveButton("确定", null)
            .show();
    }
}
